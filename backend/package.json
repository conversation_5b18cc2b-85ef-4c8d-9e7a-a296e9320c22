{"name": "<PERSON>roy<PERSON>-backend", "version": "1.0.0", "description": "Backend API for GlowRoya Persian Skincare E-commerce Platform", "main": "dist/server.js", "scripts": {"dev": "nodemon src/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:deploy": "prisma migrate deploy", "prisma:studio": "prisma studio", "prisma:seed": "ts-node prisma/seed.ts", "db:reset": "prisma migrate reset", "db:push": "prisma db push", "migrate:files": "ts-node scripts/migrate-static-files.ts", "migrate:images": "ts-node scripts/download-product-images.ts", "migrate:all": "npm run migrate:files && npm run migrate:images && npm run prisma:seed", "migrate:full": "ts-node scripts/full-migration.ts run", "migrate:status": "ts-node scripts/full-migration.ts status", "setup-db": "./scripts/setup-database.sh", "migrate-mock": "ts-node scripts/migrate-mock-data.ts", "full-migration": "ts-node scripts/run-full-migration.ts", "test-api": "ts-node scripts/test-api.ts"}, "keywords": ["ecommerce", "api", "nodejs", "typescript", "prisma", "postgresql", "persian", "skincare"], "author": "GlowRoya Team", "license": "MIT", "dependencies": {"@prisma/client": "^5.7.1", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "connect-redis": "^7.1.0", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.3.1", "dotenv-cli": "^8.0.0", "express": "^4.18.2", "express-async-errors": "^3.1.1", "express-rate-limit": "^7.1.5", "express-session": "^1.17.3", "express-validator": "^7.0.1", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7", "pg": "^8.16.0", "redis": "^4.6.11", "sharp": "^0.33.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "zod": "^3.22.4", "axios": "^1.6.2"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cookie-parser": "^1.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/express-session": "^1.17.10", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/node": "^20.10.4", "@types/nodemailer": "^6.4.14", "@types/supertest": "^2.0.16", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.6", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.55.0", "jest": "^29.7.0", "nodemon": "^3.1.10", "prisma": "^5.7.1", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "prisma": {"seed": "ts-node prisma/seed.ts"}}