#!/usr/bin/env ts-node

import axios from 'axios';

const API_BASE = 'http://localhost:3001/api/v1';

interface TestResult {
  endpoint: string;
  method: string;
  status: 'success' | 'error';
  statusCode?: number;
  message: string;
  data?: any;
}

class APITester {
  private results: TestResult[] = [];
  private authToken: string | null = null;

  async runTests(): Promise<void> {
    console.log('🧪 Starting API Tests...\n');

    try {
      // Test 1: Health Check
      await this.testHealthCheck();

      // Test 2: Authentication
      await this.testAuthentication();

      // Test 3: Categories
      await this.testCategories();

      // Test 4: Products
      await this.testProducts();

      // Test 5: Inventory (requires auth)
      if (this.authToken) {
        await this.testInventory();
        await this.testAnalytics();
      }

      // Print results
      this.printResults();

    } catch (error) {
      console.error('❌ Test suite failed:', error);
    }
  }

  private async testHealthCheck(): Promise<void> {
    try {
      const response = await axios.get(`${API_BASE.replace('/api/v1', '')}/health`);
      this.addResult('GET', '/health', 'success', response.status, 'Health check passed', response.data);
    } catch (error: any) {
      this.addResult('GET', '/health', 'error', error.response?.status, `Health check failed: ${error.message}`);
    }
  }

  private async testAuthentication(): Promise<void> {
    try {
      const response = await axios.post(`${API_BASE}/auth/login`, {
        email: '<EMAIL>',
        password: 'SuperAdmin123!'
      });
      
      if (response.data.success && response.data.data.token) {
        this.authToken = response.data.data.token;
        this.addResult('POST', '/auth/login', 'success', response.status, 'Authentication successful');
      } else {
        this.addResult('POST', '/auth/login', 'error', response.status, 'Authentication failed - no token received');
      }
    } catch (error: any) {
      this.addResult('POST', '/auth/login', 'error', error.response?.status, `Authentication failed: ${error.message}`);
    }
  }

  private async testCategories(): Promise<void> {
    try {
      const response = await axios.get(`${API_BASE}/categories`);
      this.addResult('GET', '/categories', 'success', response.status, `Found ${response.data.data?.length || 0} categories`);
    } catch (error: any) {
      this.addResult('GET', '/categories', 'error', error.response?.status, `Categories test failed: ${error.message}`);
    }
  }

  private async testProducts(): Promise<void> {
    try {
      const response = await axios.get(`${API_BASE}/products`);
      this.addResult('GET', '/products', 'success', response.status, `Found ${response.data.data?.products?.length || 0} products`);
    } catch (error: any) {
      this.addResult('GET', '/products', 'error', error.response?.status, `Products test failed: ${error.message}`);
    }
  }

  private async testInventory(): Promise<void> {
    try {
      const response = await axios.get(`${API_BASE}/inventory`, {
        headers: { Authorization: `Bearer ${this.authToken}` }
      });
      this.addResult('GET', '/inventory', 'success', response.status, `Found ${response.data.data?.inventory?.length || 0} inventory items`);
    } catch (error: any) {
      this.addResult('GET', '/inventory', 'error', error.response?.status, `Inventory test failed: ${error.message}`);
    }

    // Test inventory stats
    try {
      const response = await axios.get(`${API_BASE}/inventory/stats`, {
        headers: { Authorization: `Bearer ${this.authToken}` }
      });
      this.addResult('GET', '/inventory/stats', 'success', response.status, 'Inventory stats retrieved');
    } catch (error: any) {
      this.addResult('GET', '/inventory/stats', 'error', error.response?.status, `Inventory stats failed: ${error.message}`);
    }
  }

  private async testAnalytics(): Promise<void> {
    try {
      const response = await axios.get(`${API_BASE}/analytics/overview`, {
        headers: { Authorization: `Bearer ${this.authToken}` }
      });
      this.addResult('GET', '/analytics/overview', 'success', response.status, 'Analytics overview retrieved');
    } catch (error: any) {
      this.addResult('GET', '/analytics/overview', 'error', error.response?.status, `Analytics overview failed: ${error.message}`);
    }

    // Test sales analytics
    try {
      const response = await axios.get(`${API_BASE}/analytics/sales`, {
        headers: { Authorization: `Bearer ${this.authToken}` }
      });
      this.addResult('GET', '/analytics/sales', 'success', response.status, 'Sales analytics retrieved');
    } catch (error: any) {
      this.addResult('GET', '/analytics/sales', 'error', error.response?.status, `Sales analytics failed: ${error.message}`);
    }
  }

  private addResult(method: string, endpoint: string, status: 'success' | 'error', statusCode?: number, message?: string, data?: any): void {
    this.results.push({
      endpoint,
      method,
      status,
      statusCode,
      message: message || '',
      data
    });
  }

  private printResults(): void {
    console.log('\n📊 Test Results Summary:\n');
    
    const successCount = this.results.filter(r => r.status === 'success').length;
    const errorCount = this.results.filter(r => r.status === 'error').length;
    
    this.results.forEach(result => {
      const icon = result.status === 'success' ? '✅' : '❌';
      const statusCode = result.statusCode ? ` (${result.statusCode})` : '';
      console.log(`${icon} ${result.method} ${result.endpoint}${statusCode} - ${result.message}`);
    });

    console.log(`\n📈 Summary: ${successCount} passed, ${errorCount} failed\n`);

    if (errorCount === 0) {
      console.log('🎉 All tests passed! API is working correctly.\n');
    } else {
      console.log('⚠️ Some tests failed. Please check the API endpoints.\n');
    }
  }
}

// Run tests if called directly
if (require.main === module) {
  const tester = new APITester();
  tester.runTests().catch(console.error);
}

export { APITester };
