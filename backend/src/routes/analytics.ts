import { Router } from 'express';
import { authenticate, adminOnly } from '../middleware/auth';
import AnalyticsController from '../controllers/analyticsController';

const router = Router();

// All analytics routes require admin authentication
router.use(authenticate);
router.use(adminOnly);

// Get overview analytics
router.get('/overview', AnalyticsController.getOverview);

// Get sales analytics
router.get('/sales', AnalyticsController.getSalesAnalytics);

// Get customer analytics
router.get('/customers', AnalyticsController.getCustomerAnalytics);

// Get traffic analytics
router.get('/traffic', AnalyticsController.getTrafficAnalytics);

export default router;
