import { Router } from 'express';
import { authenticate, adminOnly } from '../middleware/auth';
import InventoryController from '../controllers/inventoryController';

const router = Router();

// All inventory routes require admin authentication
router.use(authenticate);
router.use(adminOnly);

// Get inventory data with filtering and pagination
router.get('/', InventoryController.getInventory);

// Get inventory statistics
router.get('/stats', InventoryController.getInventoryStats);

// Get low stock items
router.get('/low-stock', InventoryController.getLowStockItems);

// Update inventory for a specific product
router.put('/:id', InventoryController.updateInventory);

export default router;
