import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { z } from 'zod';

const prisma = new PrismaClient();

// Validation schemas
const analyticsFiltersSchema = z.object({
  period: z.enum(['today', 'yesterday', 'last7days', 'last30days', 'last90days', 'custom']).optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  category: z.string().optional(),
  brand: z.string().optional()
});

class AnalyticsController {
  // Get overview analytics
  static async getOverview(req: Request, res: Response): Promise<void> {
    try {
      const validation = analyticsFiltersSchema.safeParse(req.query);
      if (!validation.success) {
        res.status(400).json({
          success: false,
          message: 'پارامترهای نامعتبر',
          errors: validation.error.errors
        });
        return;
      }

      const { period = 'last30days', startDate, endDate } = validation.data;
      const dateRange = AnalyticsController.getDateRange(period, startDate, endDate);

      // Get overview metrics
      const [
        totalRevenue,
        totalOrders,
        totalCustomers,
        previousRevenue,
        previousOrders,
        averageOrderValue,
        conversionRate
      ] = await Promise.all([
        // Current period revenue
        prisma.order.aggregate({
          where: {
            createdAt: {
              gte: dateRange.start,
              lte: dateRange.end
            },
            status: { not: 'CANCELLED' }
          },
          _sum: { totalAmount: true }
        }),
        // Current period orders
        prisma.order.count({
          where: {
            createdAt: {
              gte: dateRange.start,
              lte: dateRange.end
            },
            status: { not: 'CANCELLED' }
          }
        }),
        // Total customers (all time)
        prisma.user.count({
          where: { role: 'CUSTOMER' }
        }),
        // Previous period revenue for comparison
        prisma.order.aggregate({
          where: {
            createdAt: {
              gte: dateRange.previousStart,
              lte: dateRange.previousEnd
            },
            status: { not: 'CANCELLED' }
          },
          _sum: { totalAmount: true }
        }),
        // Previous period orders for comparison
        prisma.order.count({
          where: {
            createdAt: {
              gte: dateRange.previousStart,
              lte: dateRange.previousEnd
            },
            status: { not: 'CANCELLED' }
          }
        }),
        // Average order value
        prisma.order.aggregate({
          where: {
            createdAt: {
              gte: dateRange.start,
              lte: dateRange.end
            },
            status: { not: 'CANCELLED' }
          },
          _avg: { totalAmount: true }
        }),
        // Mock conversion rate (would need web analytics integration)
        Promise.resolve(3.2)
      ]);

      // Calculate growth rates
      const revenueGrowth = AnalyticsController.calculateGrowthRate(
        Number(totalRevenue._sum.totalAmount) || 0,
        Number(previousRevenue._sum.totalAmount) || 0
      );

      const ordersGrowth = AnalyticsController.calculateGrowthRate(
        totalOrders,
        previousOrders
      );

      const overview = {
        totalRevenue: Number(totalRevenue._sum.totalAmount) || 0,
        totalOrders,
        totalCustomers,
        averageOrderValue: Number(averageOrderValue._avg.totalAmount) || 0,
        conversionRate,
        revenueGrowth,
        ordersGrowth,
        period: period,
        dateRange: {
          start: dateRange.start,
          end: dateRange.end
        }
      };

      res.json({
        success: true,
        message: 'آمار کلی با موفقیت دریافت شد',
        data: overview
      });

    } catch (error) {
      console.error('Get overview analytics error:', error);
      res.status(500).json({
        success: false,
        message: 'خطا در دریافت آمار کلی',
        error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
      });
    }
  }

  // Get sales analytics
  static async getSalesAnalytics(req: Request, res: Response): Promise<void> {
    try {
      const validation = analyticsFiltersSchema.safeParse(req.query);
      if (!validation.success) {
        res.status(400).json({
          success: false,
          message: 'پارامترهای نامعتبر',
          errors: validation.error.errors
        });
        return;
      }

      const { period = 'last30days', startDate, endDate } = validation.data;
      const dateRange = AnalyticsController.getDateRange(period, startDate, endDate);

      // Get daily sales data
      const dailySales = await prisma.$queryRaw`
        SELECT 
          DATE(created_at) as date,
          COUNT(*) as orders,
          SUM(total_amount) as revenue
        FROM orders 
        WHERE created_at >= ${dateRange.start} 
          AND created_at <= ${dateRange.end}
          AND status != 'CANCELLED'
        GROUP BY DATE(created_at)
        ORDER BY date ASC
      `;

      // Get top products (simplified)
      const topProducts = await prisma.orderItem.groupBy({
        by: ['productId'],
        where: {
          order: {
            createdAt: {
              gte: dateRange.start,
              lte: dateRange.end
            },
            status: { not: 'CANCELLED' }
          }
        },
        _sum: {
          quantity: true,
          totalPrice: true
        },
        _count: {
          id: true
        },
        orderBy: {
          _sum: {
            totalPrice: 'desc'
          }
        },
        take: 10
      });

      // Get product details for top products
      const topProductsWithDetails = await Promise.all(
        topProducts.map(async (item) => {
          const product = await prisma.product.findUnique({
            where: { id: item.productId },
            include: {
              brand: true,
              images: {
                where: { isPrimary: true },
                take: 1
              }
            }
          });

          return {
            id: product?.id,
            name: product?.name,
            brand: product?.brand?.name,
            image: product?.images[0]?.url,
            revenue: Number(item._sum.totalPrice) || 0,
            quantity: item._sum.quantity || 0,
            orders: item._count?.id || 0
          };
        })
      );

      // Get payment methods distribution (simplified)
      const orders = await prisma.order.findMany({
        where: {
          createdAt: {
            gte: dateRange.start,
            lte: dateRange.end
          },
          status: { not: 'CANCELLED' }
        },
        select: {
          paymentMethod: true,
          totalAmount: true
        }
      });

      // Group by payment method manually
      const paymentMethodsMap = new Map();
      orders.forEach(order => {
        const method = order.paymentMethod || 'UNKNOWN';
        if (!paymentMethodsMap.has(method)) {
          paymentMethodsMap.set(method, { count: 0, revenue: 0 });
        }
        const data = paymentMethodsMap.get(method);
        data.count += 1;
        data.revenue += Number(order.totalAmount);
      });

      const paymentMethods = Array.from(paymentMethodsMap.entries()).map(([method, data]) => ({
        paymentMethod: method,
        _count: { id: data.count },
        _sum: { totalAmount: data.revenue }
      }));

      const salesData = {
        dailySales: dailySales as any[],
        topProducts: topProductsWithDetails,
        paymentMethods: paymentMethods.map(method => ({
          method: method.paymentMethod,
          count: method._count?.id || 0,
          revenue: method._sum?.totalAmount || 0
        })),
        period,
        dateRange: {
          start: dateRange.start,
          end: dateRange.end
        }
      };

      res.json({
        success: true,
        message: 'آمار فروش با موفقیت دریافت شد',
        data: salesData
      });

    } catch (error) {
      console.error('Get sales analytics error:', error);
      res.status(500).json({
        success: false,
        message: 'خطا در دریافت آمار فروش',
        error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
      });
    }
  }

  // Get customer analytics
  static async getCustomerAnalytics(req: Request, res: Response): Promise<void> {
    try {
      const validation = analyticsFiltersSchema.safeParse(req.query);
      if (!validation.success) {
        res.status(400).json({
          success: false,
          message: 'پارامترهای نامعتبر',
          errors: validation.error.errors
        });
        return;
      }

      const { period = 'last30days', startDate, endDate } = validation.data;
      const dateRange = AnalyticsController.getDateRange(period, startDate, endDate);

      // Get customer metrics
      const [
        newCustomers,
        returningCustomers,
        customerLifetimeValue,
        topCustomers
      ] = await Promise.all([
        // New customers in period
        prisma.user.count({
          where: {
            role: 'CUSTOMER',
            createdAt: {
              gte: dateRange.start,
              lte: dateRange.end
            }
          }
        }),
        // Returning customers (customers who made multiple orders)
        prisma.user.count({
          where: {
            role: 'CUSTOMER',
            orders: {
              some: {
                createdAt: {
                  gte: dateRange.start,
                  lte: dateRange.end
                }
              }
            }
          }
        }),
        // Average customer lifetime value
        prisma.order.aggregate({
          where: {
            status: { not: 'CANCELLED' }
          },
          _avg: {
            totalAmount: true
          }
        }),
        // Top customers by revenue
        prisma.user.findMany({
          where: {
            role: 'CUSTOMER',
            orders: {
              some: {
                createdAt: {
                  gte: dateRange.start,
                  lte: dateRange.end
                },
                status: { not: 'CANCELLED' }
              }
            }
          },
          include: {
            orders: {
              where: {
                createdAt: {
                  gte: dateRange.start,
                  lte: dateRange.end
                },
                status: { not: 'CANCELLED' }
              }
            }
          },
          take: 10
        })
      ]);

      // Calculate top customers data
      const topCustomersData = topCustomers.map(customer => {
        const totalRevenue = customer.orders.reduce((sum, order) => sum + Number(order.totalAmount), 0);
        const orderCount = customer.orders.length;

        return {
          id: customer.id,
          name: `${customer.firstName} ${customer.lastName}`,
          email: customer.email,
          totalRevenue,
          orderCount,
          averageOrderValue: orderCount > 0 ? totalRevenue / orderCount : 0,
          lastOrderDate: customer.orders.length > 0 
            ? Math.max(...customer.orders.map(o => o.createdAt.getTime()))
            : null
        };
      }).sort((a, b) => b.totalRevenue - a.totalRevenue);

      const customerData = {
        newCustomers,
        returningCustomers,
        averageLifetimeValue: Number(customerLifetimeValue._avg.totalAmount) || 0,
        topCustomers: topCustomersData,
        period,
        dateRange: {
          start: dateRange.start,
          end: dateRange.end
        }
      };

      res.json({
        success: true,
        message: 'آمار مشتریان با موفقیت دریافت شد',
        data: customerData
      });

    } catch (error) {
      console.error('Get customer analytics error:', error);
      res.status(500).json({
        success: false,
        message: 'خطا در دریافت آمار مشتریان',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  // Get traffic analytics (mock data for now)
  static async getTrafficAnalytics(req: Request, res: Response): Promise<void> {
    try {
      // Mock traffic data - in real implementation, this would come from Google Analytics or similar
      const trafficData = {
        pageViews: 15420,
        uniqueVisitors: 8930,
        bounceRate: 42.3,
        averageSessionDuration: 245, // seconds
        topPages: [
          { path: '/', title: 'صفحه اصلی', views: 5420 },
          { path: '/products', title: 'محصولات', views: 3210 },
          { path: '/products/serums', title: 'سرم‌ها', views: 1890 },
          { path: '/about', title: 'درباره ما', views: 1120 },
          { path: '/contact', title: 'تماس با ما', views: 890 }
        ],
        deviceTypes: [
          { type: 'موبایل', percentage: 65.2, sessions: 5820 },
          { type: 'دسکتاپ', percentage: 28.7, sessions: 2560 },
          { type: 'تبلت', percentage: 6.1, sessions: 550 }
        ],
        trafficSources: [
          { source: 'جستجوی ارگانیک', percentage: 45.3, sessions: 4040 },
          { source: 'مستقیم', percentage: 32.1, sessions: 2870 },
          { source: 'شبکه‌های اجتماعی', percentage: 15.6, sessions: 1390 },
          { source: 'ایمیل مارکتینگ', percentage: 7.0, sessions: 630 }
        ]
      };

      res.json({
        success: true,
        message: 'آمار ترافیک با موفقیت دریافت شد',
        data: trafficData
      });

    } catch (error) {
      console.error('Get traffic analytics error:', error);
      res.status(500).json({
        success: false,
        message: 'خطا در دریافت آمار ترافیک',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  // Helper method to get date range based on period
  private static getDateRange(period: string, startDate?: string, endDate?: string) {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    
    let start: Date, end: Date, previousStart: Date, previousEnd: Date;

    switch (period) {
      case 'today':
        start = today;
        end = new Date(today.getTime() + 24 * 60 * 60 * 1000 - 1);
        previousStart = new Date(today.getTime() - 24 * 60 * 60 * 1000);
        previousEnd = new Date(today.getTime() - 1);
        break;
      case 'yesterday':
        start = new Date(today.getTime() - 24 * 60 * 60 * 1000);
        end = new Date(today.getTime() - 1);
        previousStart = new Date(today.getTime() - 48 * 60 * 60 * 1000);
        previousEnd = new Date(today.getTime() - 24 * 60 * 60 * 1000 - 1);
        break;
      case 'last7days':
        start = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
        end = now;
        previousStart = new Date(today.getTime() - 14 * 24 * 60 * 60 * 1000);
        previousEnd = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'last30days':
        start = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
        end = now;
        previousStart = new Date(today.getTime() - 60 * 24 * 60 * 60 * 1000);
        previousEnd = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case 'last90days':
        start = new Date(today.getTime() - 90 * 24 * 60 * 60 * 1000);
        end = now;
        previousStart = new Date(today.getTime() - 180 * 24 * 60 * 60 * 1000);
        previousEnd = new Date(today.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      case 'custom':
        start = startDate ? new Date(startDate) : new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
        end = endDate ? new Date(endDate) : now;
        const daysDiff = Math.ceil((end.getTime() - start.getTime()) / (24 * 60 * 60 * 1000));
        previousStart = new Date(start.getTime() - daysDiff * 24 * 60 * 60 * 1000);
        previousEnd = start;
        break;
      default:
        start = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
        end = now;
        previousStart = new Date(today.getTime() - 60 * 24 * 60 * 60 * 1000);
        previousEnd = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
    }

    return { start, end, previousStart, previousEnd };
  }

  // Helper method to calculate growth rate
  private static calculateGrowthRate(current: number, previous: number): number {
    if (previous === 0) return current > 0 ? 100 : 0;
    return ((current - previous) / previous) * 100;
  }
}

export default AnalyticsController;
