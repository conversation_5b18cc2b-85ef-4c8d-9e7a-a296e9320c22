# Mock Data Migration Implementation Plan
## GlowRoya E-commerce Platform - Backend API Integration

### 📋 Executive Summary
This document outlines the comprehensive migration plan to replace all mock/hardcoded data in the GlowRoya frontend with real backend API integration. Based on our systematic audit, we identified 11 major categories of mock data across 25+ files that require migration to ensure a fully functional e-commerce platform.

---

## 1. Mock Data Migration Inventory

### 🔴 **CRITICAL PRIORITY - Core Business Data**

#### **1.1 Product Data**
- **Primary File**: `src/data/products.ts`
  - **Lines**: 85-152 (`getFallbackProducts()` function)
  - **Type**: Hardcoded product array with 8 products
  - **Impact**: Core business functionality - affects all product-related features
  - **Dependencies**: Categories, brands, images, inventory

- **Static Import Usage**:
  - `src/hooks/useAdvancedSearch.ts:4` - Search functionality
  - `src/pages/ProductsPage.tsx:10` - Product listing page
  - `src/pages/ComparisonPage.tsx:9` - Product comparison
  - `src/components/product/RelatedProducts.tsx:8` - Related products

#### **1.2 Category Data**
- **Primary File**: `src/data/categories.ts`
  - **Lines**: 69-112 (`getFallbackCategories()` function)
  - **Type**: Hardcoded category array with 6 categories
  - **Impact**: Navigation, product organization, admin management
  - **Dependencies**: Product categorization

- **Static Import Usage**:
  - `src/components/admin/products/ProductForm.tsx:20` - Admin product creation
  - `src/pages/ProductsPage.tsx:11` - Category filtering

### 🟠 **HIGH PRIORITY - Admin Functionality**

#### **1.3 Inventory Management**
- **File**: `src/pages/admin/products/ProductInventoryPage.tsx`
  - **Lines**: 11-41 (`mockInventory` array)
  - **Type**: Hardcoded inventory data with stock levels
  - **Impact**: Admin inventory management completely non-functional
  - **Dependencies**: Product data, stock tracking

#### **1.4 User Management**
- **File**: `src/pages/admin/users/AdminUsersPage.tsx`
  - **Lines**: 59-65 (TODO comments, empty arrays)
  - **Type**: Missing API integration
  - **Impact**: Admin user management non-functional
  - **Dependencies**: Authentication system

#### **1.5 Admin Analytics**
- **File**: `src/hooks/useAdminAnalytics.ts`
  - **Lines**: 40-50 (TODO comments, null values)
  - **Type**: Missing API integration
  - **Impact**: Admin dashboard shows no data
  - **Dependencies**: Order data, customer data, traffic data

- **File**: `src/pages/admin/analytics/TrafficAnalyticsPage.tsx`
  - **Lines**: 41-50 (`mockTrafficData` object)
  - **Type**: Hardcoded analytics data
  - **Impact**: Traffic analytics showing fake data

### 🟡 **MEDIUM PRIORITY - Customer Features**

#### **1.6 Review System**
- **File**: `src/hooks/useReviews.ts`
  - **Lines**: 12-33 (`generateMockReviews()` function)
  - **Type**: Mock review generation function
  - **Impact**: Product reviews using fake data

- **File**: `src/components/product/ProductReviews.tsx`
  - **Lines**: 21-49 (`mockReviews` array)
  - **Type**: Hardcoded review array
  - **Impact**: Product review display using static data

- **File**: `src/pages/admin/reviews/ReviewDetailsPage.tsx`
  - **Lines**: 59-79 (`mockReview` object)
  - **Type**: Hardcoded review object
  - **Impact**: Admin review management using fake data

#### **1.7 Testimonials**
- **File**: `src/data/testimonials.ts`
  - **Lines**: 3-49 (entire file)
  - **Type**: Hardcoded testimonial array
  - **Impact**: Customer testimonials using static data
  - **Dependencies**: Customer data, review system

#### **1.8 Admin Notifications**
- **File**: `src/hooks/useAdminNotifications.ts`
  - **Lines**: 31-39 (TODO comments, empty arrays)
  - **Type**: Missing API integration
  - **Impact**: Admin notification system non-functional

- **File**: `src/contexts/AdminNotificationsContext.tsx`
  - **Lines**: 41-46 (TODO comments, empty arrays)
  - **Type**: Missing API integration
  - **Impact**: Notification context not functional

### 🟢 **LOW PRIORITY - Content Management**

#### **1.9 Home Page Content**
- **File**: `src/hooks/useHomePageContent.ts`
  - **Lines**: 13-47 (`generateMockHomePageContent()` function)
  - **Type**: Mock content generation with localStorage storage
  - **Impact**: Home page content management using mock data
  - **Dependencies**: Content management system

#### **1.10 LocalStorage Dependencies**
- **Cart Data**: 
  - `src/context/CartContext.tsx` - Cart persistence
  - `src/utils/cartUtils.ts:260-308` - Cart storage utilities
- **Wishlist Data**: 
  - `src/context/WishlistContext.tsx:23-44` - Wishlist storage
- **Comparison Data**: 
  - `src/hooks/useProductComparison.ts:14-18` - Comparison storage
- **Loyalty Data**: 
  - `src/hooks/useLoyalty.ts:111-118` - Loyalty member data
- **Search History**: 
  - `src/utils/persianSearch.ts` - Search analytics storage

#### **1.11 Development Mock Data (Remove in Production)**
- **File**: `src/utils/authUtils.ts`
  - **Lines**: 244-353 (`MockAuthAPI` class)
  - **Type**: Mock authentication API for development
  - **Impact**: Should be removed/disabled in production
  - **Dependencies**: Authentication system

---

## 2. Backend Data Models and Structure

### **2.1 Required Database Models**

#### **Product Model Enhancement**
```typescript
interface BackendProduct {
  id: string;
  name: string;
  nameEn?: string;
  slug: string;
  description: string;
  shortDescription?: string;
  sku: string;
  barcode?: string;
  price: number;
  comparePrice?: number;
  costPrice?: number;
  brand: {
    id: string;
    name: string;
    slug: string;
    logo?: string;
  };
  categories: Array<{
    id: string;
    name: string;
    slug: string;
  }>;
  images: Array<{
    id: string;
    url: string;
    alt?: string;
    position: number;
  }>;
  inventory: {
    quantity: number;
    lowStockThreshold: number;
    trackQuantity: boolean;
    allowBackorder: boolean;
  };
  seo: {
    title?: string;
    description?: string;
    keywords?: string[];
  };
  status: 'active' | 'draft' | 'archived';
  visibility: 'visible' | 'hidden';
  isFeatured: boolean;
  isNew: boolean;
  isBestSeller: boolean;
  tags: string[];
  benefits?: string[];
  ingredients?: string[];
  howToUse?: string[];
  skinType?: string[];
  size?: string;
  weight?: number;
  createdAt: string;
  updatedAt: string;
}
```

#### **Review Model**
```typescript
interface BackendReview {
  id: string;
  productId: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  rating: number;
  title: string;
  comment: string;
  pros: string[];
  cons: string[];
  images?: string[];
  isVerifiedPurchase: boolean;
  isRecommended: boolean;
  helpfulVotes: number;
  totalVotes: number;
  skinType?: string;
  ageRange?: string;
  usageDuration?: string;
  moderationStatus: 'pending' | 'approved' | 'rejected';
  createdAt: string;
  updatedAt: string;
}
```

#### **Analytics Model**
```typescript
interface BackendAnalytics {
  overview: {
    totalRevenue: number;
    totalOrders: number;
    totalCustomers: number;
    averageOrderValue: number;
    conversionRate: number;
    period: string;
  };
  sales: {
    dailySales: Array<{
      date: string;
      revenue: number;
      orders: number;
    }>;
    topProducts: Array<{
      productId: string;
      name: string;
      revenue: number;
      quantity: number;
    }>;
  };
  traffic: {
    pageViews: number;
    uniqueVisitors: number;
    bounceRate: number;
    averageSessionDuration: number;
    topPages: Array<{
      path: string;
      title: string;
      views: number;
    }>;
  };
}
```

### **2.2 Required API Endpoints**

#### **Product Endpoints**
- `GET /api/v1/products` - Get products with filtering/pagination
- `GET /api/v1/products/:id` - Get single product
- `GET /api/v1/products/featured` - Get featured products
- `GET /api/v1/products/new` - Get new products
- `GET /api/v1/products/bestsellers` - Get bestselling products
- `GET /api/v1/products/discounted` - Get discounted products
- `GET /api/v1/products/search` - Search products
- `POST /api/v1/admin/products` - Create product (admin)
- `PUT /api/v1/admin/products/:id` - Update product (admin)
- `DELETE /api/v1/admin/products/:id` - Delete product (admin)

#### **Review Endpoints**
- `GET /api/v1/products/:id/reviews` - Get product reviews
- `POST /api/v1/products/:id/reviews` - Create review
- `PUT /api/v1/reviews/:id/helpful` - Mark review helpful
- `GET /api/v1/admin/reviews` - Get all reviews (admin)
- `PUT /api/v1/admin/reviews/:id/moderate` - Moderate review (admin)

#### **Analytics Endpoints**
- `GET /api/v1/admin/analytics/overview` - Get overview analytics
- `GET /api/v1/admin/analytics/sales` - Get sales analytics
- `GET /api/v1/admin/analytics/traffic` - Get traffic analytics
- `GET /api/v1/admin/analytics/customers` - Get customer analytics

#### **Inventory Endpoints**
- `GET /api/v1/admin/inventory` - Get inventory data
- `PUT /api/v1/admin/inventory/:id` - Update inventory
- `GET /api/v1/admin/inventory/low-stock` - Get low stock items

#### **User Management Endpoints**
- `GET /api/v1/admin/users` - Get admin users
- `POST /api/v1/admin/users` - Create admin user
- `PUT /api/v1/admin/users/:id` - Update admin user
- `DELETE /api/v1/admin/users/:id` - Delete admin user

#### **Notification Endpoints**
- `GET /api/v1/admin/notifications` - Get notifications
- `POST /api/v1/admin/notifications` - Create notification
- `PUT /api/v1/admin/notifications/:id/read` - Mark as read

#### **Content Management Endpoints**
- `GET /api/v1/content/home` - Get home page content
- `PUT /api/v1/admin/content/home` - Update home page content
- `GET /api/v1/content/testimonials` - Get testimonials
- `POST /api/v1/admin/testimonials` - Create testimonial

### **2.3 Data Transformation Requirements**

#### **Frontend to Backend Mapping**
```typescript
// Transform frontend Product to backend format
const transformToBackendProduct = (frontendProduct: Product): BackendProduct => {
  return {
    id: frontendProduct.id.toString(),
    name: frontendProduct.name,
    slug: generateSlug(frontendProduct.name),
    description: frontendProduct.description,
    sku: frontendProduct.sku || generateSKU(),
    price: frontendProduct.price,
    comparePrice: frontendProduct.discountedPrice,
    brand: {
      id: frontendProduct.brand,
      name: frontendProduct.brand,
      slug: generateSlug(frontendProduct.brand)
    },
    categories: [{
      id: frontendProduct.category,
      name: frontendProduct.category,
      slug: generateSlug(frontendProduct.category)
    }],
    images: frontendProduct.images.map((url, index) => ({
      id: `img-${index}`,
      url,
      position: index
    })),
    inventory: {
      quantity: frontendProduct.stock || 0,
      lowStockThreshold: 10,
      trackQuantity: true,
      allowBackorder: false
    },
    status: 'active',
    visibility: 'visible',
    isFeatured: frontendProduct.featured || false,
    isNew: frontendProduct.isNew || false,
    isBestSeller: frontendProduct.isBestSeller || false,
    tags: frontendProduct.tags || [],
    benefits: frontendProduct.benefits || [],
    ingredients: frontendProduct.ingredients || [],
    howToUse: frontendProduct.howToUse ? [frontendProduct.howToUse] : [],
    skinType: frontendProduct.skinType || [],
    size: frontendProduct.size,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };
};
```

---

## 3. Media and Static Assets Migration Plan

### **3.1 Current Static Assets Inventory**

#### **Product Images**
- **Location**: Hardcoded URLs in `src/data/products.ts`
- **Pattern**: `/uploads/products/{category}/{product-name}-{variant}.jpg`
- **Count**: ~24 product images across 8 products
- **Status**: ✅ Already migrated to backend uploads directory

#### **Brand Logos**
- **Location**: Referenced in brand utilities and product data
- **Pattern**: `/uploads/brands/{brand-slug}-logo.{ext}`
- **Count**: ~4 brand logos (CeraVe, Garnier, Nivea, L'Oréal)
- **Status**: ✅ Already migrated to backend uploads directory

#### **Category Images**
- **Location**: Hardcoded in `src/data/categories.ts`
- **Pattern**: `/uploads/categories/{category-slug}-banner.jpg`
- **Count**: 6 category banner images
- **Status**: ⚠️ Needs verification and API integration

#### **App Assets**
- **Location**: Various components and public directory
- **Items**: App logo, favicon, PWA icons
- **Status**: ✅ Already migrated to backend uploads directory

### **3.2 Media Serving Strategy**

#### **URL Pattern**
```
Production: https://*************/uploads/{type}/{filename}
Development: http://localhost:3001/uploads/{type}/{filename}
```

#### **Image Optimization Requirements**
- **Responsive Images**: Multiple sizes (thumbnail, medium, large)
- **Format Support**: WebP with JPEG fallback
- **Lazy Loading**: Implemented in `OptimizedProductImage` component
- **CDN Integration**: Future enhancement for performance

### **3.3 Asset Migration Tasks**
1. ✅ **Product Images**: Already migrated and serving correctly
2. ✅ **Brand Logos**: Already migrated and serving correctly  
3. ⚠️ **Category Images**: Need API integration for dynamic loading
4. ⚠️ **User Avatars**: Need upload system for user profiles
5. ⚠️ **Review Images**: Need upload system for review attachments

---

## 4. Implementation Task List

### **Phase 1: Core Admin Functionality (Priority 1)**

#### **Task 1.1: Product Data API Integration**
- **Complexity**: High
- **Estimated Effort**: 2-3 days
- **Files to Modify**:
  - `src/hooks/useAdvancedSearch.ts` - Replace static import with API call
  - `src/pages/ProductsPage.tsx` - Replace static import with API call
  - `src/pages/ComparisonPage.tsx` - Replace static import with API call
  - `src/components/product/RelatedProducts.tsx` - Replace static import with API call
- **API Endpoints**: 
  - `GET /api/v1/products` (enhance existing)
  - `GET /api/v1/products/search` (new)
  - `GET /api/v1/products/related/:id` (new)
- **Success Criteria**:
  - All product pages load data from API
  - Search functionality works with backend
  - Related products show dynamic recommendations
  - Fallback to cached data when API unavailable
- **Testing**: Verify with production backend, test offline scenarios

#### **Task 1.2: Category Data API Integration**
- **Complexity**: Medium
- **Estimated Effort**: 1-2 days
- **Files to Modify**:
  - `src/components/admin/products/ProductForm.tsx` - Replace static import
  - `src/pages/ProductsPage.tsx` - Replace static import for filtering
  - `src/data/categories.ts` - Convert to API-based loading
- **API Endpoints**: 
  - `GET /api/v1/categories` (enhance existing)
- **Success Criteria**:
  - Admin product form loads categories from API
  - Product filtering uses dynamic categories
  - Category navigation reflects backend data
- **Testing**: Verify category CRUD operations in admin

#### **Task 1.3: Inventory Management API Integration**
- **Complexity**: High
- **Estimated Effort**: 2-3 days
- **Files to Modify**:
  - `src/pages/admin/products/ProductInventoryPage.tsx` - Replace mock data
  - `src/hooks/useAdminProducts.ts` - Add inventory methods
- **API Endpoints**: 
  - `GET /api/v1/admin/inventory` (new)
  - `PUT /api/v1/admin/inventory/:id` (new)
  - `GET /api/v1/admin/inventory/low-stock` (new)
- **Success Criteria**:
  - Real-time inventory data display
  - Stock level updates work correctly
  - Low stock alerts functional
  - Inventory history tracking
- **Testing**: Test stock updates, verify data consistency

#### **Task 1.4: User Management API Integration**
- **Complexity**: High
- **Estimated Effort**: 2-3 days
- **Files to Modify**:
  - `src/pages/admin/users/AdminUsersPage.tsx` - Implement API calls
  - Create `src/services/adminUserService.ts` - New service
- **API Endpoints**: 
  - `GET /api/v1/admin/users` (new)
  - `POST /api/v1/admin/users` (new)
  - `PUT /api/v1/admin/users/:id` (new)
  - `DELETE /api/v1/admin/users/:id` (new)
- **Success Criteria**:
  - Admin user CRUD operations work
  - Role and permission management functional
  - User authentication integration
- **Testing**: Test user creation, role assignments, permissions

### **Phase 2: Customer-Facing Features (Priority 2)**

#### **Task 2.1: Review System API Integration**
- **Complexity**: High
- **Estimated Effort**: 3-4 days
- **Files to Modify**:
  - `src/hooks/useReviews.ts` - Replace mock data with API calls
  - `src/components/product/ProductReviews.tsx` - Replace static reviews
  - `src/pages/admin/reviews/ReviewDetailsPage.tsx` - Replace mock review
- **API Endpoints**: 
  - `GET /api/v1/products/:id/reviews` (enhance existing)
  - `POST /api/v1/products/:id/reviews` (enhance existing)
  - `PUT /api/v1/reviews/:id/helpful` (new)
  - `GET /api/v1/admin/reviews` (new)
  - `PUT /api/v1/admin/reviews/:id/moderate` (new)
- **Success Criteria**:
  - Product reviews load from backend
  - Review submission works correctly
  - Admin review moderation functional
  - Review voting system works
- **Testing**: Test review CRUD, moderation workflow, voting

#### **Task 2.2: Cart/Wishlist Backend Persistence**
- **Complexity**: Medium
- **Estimated Effort**: 2-3 days
- **Files to Modify**:
  - `src/context/CartContext.tsx` - Add API persistence
  - `src/context/WishlistContext.tsx` - Add API persistence
  - `src/utils/cartUtils.ts` - Add API storage methods
- **API Endpoints**: 
  - `GET /api/v1/cart` (new)
  - `POST /api/v1/cart/items` (new)
  - `PUT /api/v1/cart/items/:id` (new)
  - `DELETE /api/v1/cart/items/:id` (new)
  - `GET /api/v1/wishlist` (enhance existing)
  - `POST /api/v1/wishlist/items` (enhance existing)
- **Success Criteria**:
  - Cart persists across sessions
  - Wishlist syncs with backend
  - Guest cart migration to user account
- **Testing**: Test cart persistence, user login scenarios

### **Phase 3: Content Management and Analytics (Priority 3)**

#### **Task 3.1: Admin Analytics API Integration**
- **Complexity**: High
- **Estimated Effort**: 3-4 days
- **Files to Modify**:
  - `src/hooks/useAdminAnalytics.ts` - Implement API calls
  - `src/pages/admin/analytics/TrafficAnalyticsPage.tsx` - Replace mock data
- **API Endpoints**: 
  - `GET /api/v1/admin/analytics/overview` (new)
  - `GET /api/v1/admin/analytics/sales` (new)
  - `GET /api/v1/admin/analytics/traffic` (new)
  - `GET /api/v1/admin/analytics/customers` (new)
- **Success Criteria**:
  - Real analytics data display
  - Date range filtering works
  - Export functionality operational
- **Testing**: Verify analytics accuracy, test date filtering

#### **Task 3.2: Notification System API Integration**
- **Complexity**: Medium
- **Estimated Effort**: 2 days
- **Files to Modify**:
  - `src/hooks/useAdminNotifications.ts` - Implement API calls
  - `src/contexts/AdminNotificationsContext.tsx` - Add API integration
- **API Endpoints**: 
  - `GET /api/v1/admin/notifications` (new)
  - `POST /api/v1/admin/notifications` (new)
  - `PUT /api/v1/admin/notifications/:id/read` (new)
- **Success Criteria**:
  - Real-time notifications work
  - Notification CRUD operations functional
  - Read/unread status tracking
- **Testing**: Test notification creation, real-time updates

#### **Task 3.3: Content Management API Integration**
- **Complexity**: Medium
- **Estimated Effort**: 2-3 days
- **Files to Modify**:
  - `src/hooks/useHomePageContent.ts` - Replace localStorage with API
  - `src/data/testimonials.ts` - Convert to API-based loading
- **API Endpoints**: 
  - `GET /api/v1/content/home` (new)
  - `PUT /api/v1/admin/content/home` (new)
  - `GET /api/v1/content/testimonials` (new)
  - `POST /api/v1/admin/testimonials` (new)
- **Success Criteria**:
  - Home page content editable via admin
  - Testimonials managed through backend
  - Content versioning support
- **Testing**: Test content updates, verify frontend display

### **Phase 4: Secondary Features (Priority 4)**

#### **Task 4.1: Loyalty Program API Integration**
- **Complexity**: Medium
- **Estimated Effort**: 2 days
- **Files to Modify**:
  - `src/hooks/useLoyalty.ts` - Replace localStorage with API
- **API Endpoints**: 
  - `GET /api/v1/loyalty/member` (enhance existing)
  - `POST /api/v1/loyalty/earn` (enhance existing)
  - `POST /api/v1/loyalty/redeem` (enhance existing)
- **Success Criteria**:
  - Loyalty points sync with backend
  - Point earning/redemption works
  - Member tier calculation accurate
- **Testing**: Test point transactions, tier calculations

#### **Task 4.2: Search Analytics and History**
- **Complexity**: Low
- **Estimated Effort**: 1 day
- **Files to Modify**:
  - `src/utils/persianSearch.ts` - Add API analytics tracking
- **API Endpoints**: 
  - `POST /api/v1/analytics/search` (new)
  - `GET /api/v1/search/suggestions` (new)
- **Success Criteria**:
  - Search analytics tracked in backend
  - Search suggestions based on real data
- **Testing**: Test search tracking, verify suggestions

---

## 5. Quality Assurance Requirements

### **5.1 Testing Procedures**

#### **API Integration Testing**
1. **Unit Tests**: Test each API service method
2. **Integration Tests**: Test component-API interaction
3. **Error Handling Tests**: Test API failure scenarios
4. **Performance Tests**: Test loading times and caching
5. **Data Consistency Tests**: Verify data integrity across operations

#### **User Experience Testing**
1. **Loading States**: Verify proper loading indicators
2. **Error Messages**: Test Persian error message display
3. **Offline Behavior**: Test fallback mechanisms
4. **Mobile Responsiveness**: Verify mobile functionality
5. **RTL Support**: Test Persian/RTL layout integrity

#### **Backend Integration Testing**
1. **Production Server**: Test with *************
2. **Authentication**: Verify admin/user authentication
3. **CRUD Operations**: Test all create/read/update/delete operations
4. **File Uploads**: Test image and media uploads
5. **Database Consistency**: Verify data persistence

### **5.2 Error Handling and Fallback Mechanisms**

#### **API Failure Handling**
```typescript
// Example error handling pattern
const handleApiError = (error: Error, fallbackData?: any) => {
  // Log error for monitoring
  console.error('API Error:', error);
  
  // Show Persian error message to user
  toast.error(getErrorMessage(error));
  
  // Return fallback data if available
  if (fallbackData) {
    return fallbackData;
  }
  
  // Return empty state
  return getEmptyState();
};
```

#### **Caching Strategy**
- **Product Data**: 5-minute cache with background refresh
- **Category Data**: 10-minute cache (less frequent changes)
- **User Data**: Session-based cache
- **Analytics Data**: 1-minute cache for real-time feel

#### **Offline Support**
- **Service Worker**: Cache critical API responses
- **LocalStorage Fallback**: Maintain last known good state
- **Progressive Enhancement**: Core functionality works offline

### **5.3 Persian/RTL Language Support Verification**

#### **Text Direction Testing**
- Verify all new API-loaded content displays correctly in RTL
- Test form inputs with Persian text
- Verify date/number formatting in Persian locale

#### **Error Message Localization**
- All API error messages translated to Persian
- Consistent error message formatting
- User-friendly error descriptions

#### **Content Validation**
- Persian text search functionality
- Persian character support in all fields
- Proper Persian typography and spacing

### **5.4 Performance and User Experience Preservation**

#### **Loading Performance**
- **Target**: Page load time < 3 seconds
- **API Response Time**: < 500ms for critical endpoints
- **Image Loading**: Progressive loading with placeholders
- **Caching**: Aggressive caching for static content

#### **User Experience Metrics**
- **First Contentful Paint**: < 1.5 seconds
- **Largest Contentful Paint**: < 2.5 seconds
- **Cumulative Layout Shift**: < 0.1
- **First Input Delay**: < 100ms

#### **Accessibility Requirements**
- **WCAG 2.1 AA Compliance**: Maintain current accessibility level
- **Screen Reader Support**: Test with Persian screen readers
- **Keyboard Navigation**: Ensure all functionality keyboard accessible
- **Color Contrast**: Maintain 4.5:1 contrast ratio

---

## 6. Implementation Timeline and Milestones

### **Week 1-2: Phase 1 - Core Admin Functionality**
- **Days 1-3**: Product Data API Integration (Task 1.1)
- **Days 4-5**: Category Data API Integration (Task 1.2)
- **Days 6-8**: Inventory Management API Integration (Task 1.3)
- **Days 9-11**: User Management API Integration (Task 1.4)
- **Days 12-14**: Testing and bug fixes

### **Week 3-4: Phase 2 - Customer-Facing Features**
- **Days 15-18**: Review System API Integration (Task 2.1)
- **Days 19-21**: Cart/Wishlist Backend Persistence (Task 2.2)
- **Days 22-28**: Testing and optimization

### **Week 5-6: Phase 3 - Content Management and Analytics**
- **Days 29-32**: Admin Analytics API Integration (Task 3.1)
- **Days 33-34**: Notification System API Integration (Task 3.2)
- **Days 35-37**: Content Management API Integration (Task 3.3)
- **Days 38-42**: Testing and refinement

### **Week 7: Phase 4 - Secondary Features and Final Testing**
- **Days 43-44**: Loyalty Program API Integration (Task 4.1)
- **Day 45**: Search Analytics and History (Task 4.2)
- **Days 46-49**: Comprehensive testing and bug fixes

### **Success Metrics**
- ✅ 0 mock data imports remaining in production code
- ✅ All admin functionality using real backend APIs
- ✅ All customer features using real backend APIs
- ✅ Performance metrics maintained or improved
- ✅ Persian/RTL support preserved throughout
- ✅ Error handling and fallback mechanisms functional
- ✅ Production backend integration successful

---

## 7. Risk Mitigation and Contingency Plans

### **High-Risk Areas**
1. **Backend API Availability**: Ensure robust fallback mechanisms
2. **Data Migration Integrity**: Implement data validation and rollback procedures
3. **Performance Impact**: Monitor and optimize API response times
4. **User Experience Disruption**: Maintain feature parity during migration

### **Contingency Plans**
1. **API Downtime**: Graceful degradation to cached/fallback data
2. **Data Corruption**: Database backup and restore procedures
3. **Performance Issues**: Implement progressive loading and caching
4. **User Complaints**: Clear communication and quick rollback capability

---

## 8. Technical Implementation Guidelines

### **8.1 API Service Layer Architecture**

#### **Service Layer Pattern**
All API integrations must follow the established service layer pattern:

```typescript
// Example: Enhanced Product Service
class ProductApiService {
  private static baseUrl = `${API_BASE_URL}/api/v1`;

  static async getProducts(filters?: ProductFilters): Promise<Product[]> {
    try {
      const response = await fetch(`${this.baseUrl}/products`, {
        method: 'GET',
        headers: this.getHeaders(),
        body: filters ? JSON.stringify(filters) : undefined
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return data.data.products.map(transformBackendProduct);
    } catch (error) {
      console.error('Product API Error:', error);
      throw new ApiError(error.message, 'PRODUCT_FETCH_FAILED');
    }
  }

  private static getHeaders(): HeadersInit {
    const token = AuthStorage.getToken();
    return {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` })
    };
  }
}
```

#### **Error Handling Standards**
```typescript
// Standardized error handling
export class ApiError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode?: number
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

// Persian error message mapping
export const PERSIAN_API_ERRORS: Record<string, string> = {
  'PRODUCT_FETCH_FAILED': 'خطا در بارگذاری محصولات',
  'CATEGORY_FETCH_FAILED': 'خطا در بارگذاری دسته‌بندی‌ها',
  'REVIEW_SUBMIT_FAILED': 'خطا در ثبت نظر',
  'INVENTORY_UPDATE_FAILED': 'خطا در به‌روزرسانی موجودی',
  'USER_CREATE_FAILED': 'خطا در ایجاد کاربر',
  'ANALYTICS_FETCH_FAILED': 'خطا در بارگذاری آمار',
  'NOTIFICATION_FETCH_FAILED': 'خطا در بارگذاری اعلان‌ها',
  'CONTENT_UPDATE_FAILED': 'خطا در به‌روزرسانی محتوا',
  'NETWORK_ERROR': 'خطا در اتصال به سرور',
  'UNAUTHORIZED': 'دسترسی غیرمجاز',
  'FORBIDDEN': 'عدم دسترسی',
  'NOT_FOUND': 'یافت نشد',
  'SERVER_ERROR': 'خطای سرور'
};
```

### **8.2 Data Transformation Layer**

#### **Consistent Data Transformation**
```typescript
// Backend to Frontend transformation
export const transformBackendProduct = (backendProduct: BackendProduct): Product => {
  return {
    id: parseInt(backendProduct.id) || Math.floor(Math.random() * 1000000),
    name: backendProduct.name,
    category: backendProduct.categories?.[0]?.name || 'عمومی',
    brand: backendProduct.brand?.name || '',
    price: parseFloat(backendProduct.price.toString()) || 0,
    discountedPrice: backendProduct.comparePrice ? parseFloat(backendProduct.comparePrice.toString()) : undefined,
    rating: 0, // Calculate from reviews
    reviewCount: 0, // Get from reviews
    imageSrc: backendProduct.images?.[0]?.url ? getFullImageUrl(backendProduct.images[0].url) : '/images/products/placeholder.jpg',
    images: backendProduct.images?.length > 0
      ? backendProduct.images.map(img => getFullImageUrl(img.url))
      : ['/images/products/placeholder.jpg'],
    description: backendProduct.description || '',
    ingredients: backendProduct.ingredients || [],
    benefits: backendProduct.benefits || [],
    howToUse: backendProduct.howToUse?.join('\n') || '',
    skinType: backendProduct.skinType || [],
    size: backendProduct.size || '',
    inStock: (backendProduct.inventory?.quantity || 0) > 0,
    stock: backendProduct.inventory?.quantity || 0,
    featured: backendProduct.isFeatured || false,
    isNew: backendProduct.isNew || false,
    isBestSeller: backendProduct.isBestSeller || false,
    tags: backendProduct.tags || []
  };
};

// Frontend to Backend transformation
export const transformToBackendProduct = (frontendProduct: Product): Partial<BackendProduct> => {
  return {
    name: frontendProduct.name,
    description: frontendProduct.description,
    price: frontendProduct.price,
    comparePrice: frontendProduct.discountedPrice,
    isFeatured: frontendProduct.featured,
    isNew: frontendProduct.isNew,
    isBestSeller: frontendProduct.isBestSeller,
    tags: frontendProduct.tags,
    benefits: frontendProduct.benefits,
    ingredients: frontendProduct.ingredients,
    howToUse: frontendProduct.howToUse ? [frontendProduct.howToUse] : [],
    skinType: frontendProduct.skinType,
    size: frontendProduct.size,
    inventory: {
      quantity: frontendProduct.stock || 0,
      lowStockThreshold: 10,
      trackQuantity: true,
      allowBackorder: false
    }
  };
};
```

### **8.3 Caching and Performance Strategy**

#### **Multi-Level Caching**
```typescript
// Cache configuration
export const CACHE_CONFIG = {
  PRODUCTS: {
    duration: 5 * 60 * 1000, // 5 minutes
    key: 'products_cache'
  },
  CATEGORIES: {
    duration: 10 * 60 * 1000, // 10 minutes
    key: 'categories_cache'
  },
  USER_DATA: {
    duration: 30 * 60 * 1000, // 30 minutes
    key: 'user_cache'
  },
  ANALYTICS: {
    duration: 1 * 60 * 1000, // 1 minute
    key: 'analytics_cache'
  }
};

// Cache implementation
class ApiCache {
  private static cache = new Map<string, { data: any; timestamp: number; duration: number }>();

  static set(key: string, data: any, duration: number): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      duration
    });
  }

  static get(key: string): any | null {
    const cached = this.cache.get(key);
    if (!cached) return null;

    if (Date.now() - cached.timestamp > cached.duration) {
      this.cache.delete(key);
      return null;
    }

    return cached.data;
  }

  static invalidate(key: string): void {
    this.cache.delete(key);
  }

  static clear(): void {
    this.cache.clear();
  }
}
```

### **8.4 State Management Integration**

#### **React Query Integration**
```typescript
// Example: Product data with React Query
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';

export const useProducts = (filters?: ProductFilters) => {
  return useQuery({
    queryKey: ['products', filters],
    queryFn: () => ProductApiService.getProducts(filters),
    staleTime: CACHE_CONFIG.PRODUCTS.duration,
    cacheTime: CACHE_CONFIG.PRODUCTS.duration * 2,
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    onError: (error) => {
      toast.error(PERSIAN_API_ERRORS[error.code] || 'خطای غیرمنتظره');
    }
  });
};

export const useCreateProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ProductApiService.createProduct,
    onSuccess: () => {
      queryClient.invalidateQueries(['products']);
      toast.success('محصول با موفقیت ایجاد شد');
    },
    onError: (error) => {
      toast.error(PERSIAN_API_ERRORS[error.code] || 'خطا در ایجاد محصول');
    }
  });
};
```

---

## 9. Monitoring and Analytics

### **9.1 API Performance Monitoring**

#### **Performance Metrics**
```typescript
// API performance tracking
class ApiPerformanceMonitor {
  private static metrics = new Map<string, {
    totalRequests: number;
    totalTime: number;
    errors: number;
    lastRequest: number;
  }>();

  static startRequest(endpoint: string): string {
    const requestId = `${endpoint}_${Date.now()}_${Math.random()}`;
    return requestId;
  }

  static endRequest(endpoint: string, requestId: string, success: boolean): void {
    const startTime = parseInt(requestId.split('_')[1]);
    const duration = Date.now() - startTime;

    const current = this.metrics.get(endpoint) || {
      totalRequests: 0,
      totalTime: 0,
      errors: 0,
      lastRequest: 0
    };

    this.metrics.set(endpoint, {
      totalRequests: current.totalRequests + 1,
      totalTime: current.totalTime + duration,
      errors: current.errors + (success ? 0 : 1),
      lastRequest: Date.now()
    });

    // Log slow requests
    if (duration > 2000) {
      console.warn(`Slow API request: ${endpoint} took ${duration}ms`);
    }
  }

  static getMetrics(): Record<string, any> {
    const result: Record<string, any> = {};

    this.metrics.forEach((metrics, endpoint) => {
      result[endpoint] = {
        ...metrics,
        averageTime: metrics.totalRequests > 0 ? metrics.totalTime / metrics.totalRequests : 0,
        errorRate: metrics.totalRequests > 0 ? (metrics.errors / metrics.totalRequests) * 100 : 0
      };
    });

    return result;
  }
}
```

### **9.2 Error Tracking and Reporting**

#### **Error Reporting System**
```typescript
// Error reporting for production monitoring
class ErrorReporter {
  static reportError(error: Error, context: {
    component?: string;
    action?: string;
    userId?: string;
    additionalData?: any;
  }): void {
    const errorReport = {
      message: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      ...context
    };

    // Send to monitoring service (e.g., Sentry, LogRocket)
    console.error('Error Report:', errorReport);

    // In production, send to backend
    if (process.env.NODE_ENV === 'production') {
      fetch('/api/v1/errors', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(errorReport)
      }).catch(() => {
        // Silently fail if error reporting fails
      });
    }
  }
}
```

### **9.3 User Experience Monitoring**

#### **UX Metrics Tracking**
```typescript
// Track user experience metrics during migration
class UXMonitor {
  static trackPageLoad(pageName: string, loadTime: number): void {
    const metric = {
      page: pageName,
      loadTime,
      timestamp: Date.now(),
      userAgent: navigator.userAgent
    };

    // Store locally and batch send
    const metrics = JSON.parse(localStorage.getItem('ux_metrics') || '[]');
    metrics.push(metric);

    // Keep only last 100 metrics
    if (metrics.length > 100) {
      metrics.splice(0, metrics.length - 100);
    }

    localStorage.setItem('ux_metrics', JSON.stringify(metrics));

    // Send batch if we have enough data
    if (metrics.length >= 10) {
      this.sendMetrics(metrics);
      localStorage.removeItem('ux_metrics');
    }
  }

  private static async sendMetrics(metrics: any[]): Promise<void> {
    try {
      await fetch('/api/v1/analytics/ux', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ metrics })
      });
    } catch (error) {
      // Restore metrics if send failed
      const existing = JSON.parse(localStorage.getItem('ux_metrics') || '[]');
      localStorage.setItem('ux_metrics', JSON.stringify([...existing, ...metrics]));
    }
  }
}
```

---

## 10. Deployment and Rollback Strategy

### **10.1 Phased Deployment Plan**

#### **Blue-Green Deployment Strategy**
1. **Blue Environment**: Current production with mock data
2. **Green Environment**: New version with API integration
3. **Traffic Splitting**: Gradually shift traffic from blue to green
4. **Monitoring**: Real-time monitoring of both environments
5. **Rollback**: Instant rollback capability if issues detected

#### **Feature Flags for Gradual Rollout**
```typescript
// Feature flag system for controlled rollout
class FeatureFlags {
  private static flags: Record<string, boolean> = {
    'api_products': false,
    'api_categories': false,
    'api_reviews': false,
    'api_analytics': false,
    'api_notifications': false
  };

  static isEnabled(flag: string): boolean {
    // Check remote config first, fallback to local
    return this.getRemoteFlag(flag) ?? this.flags[flag] ?? false;
  }

  private static getRemoteFlag(flag: string): boolean | null {
    try {
      const config = JSON.parse(localStorage.getItem('feature_flags') || '{}');
      return config[flag] ?? null;
    } catch {
      return null;
    }
  }

  static async updateFlags(): Promise<void> {
    try {
      const response = await fetch('/api/v1/config/features');
      const flags = await response.json();
      localStorage.setItem('feature_flags', JSON.stringify(flags));
    } catch (error) {
      console.warn('Failed to update feature flags:', error);
    }
  }
}

// Usage in components
const ProductsPage = () => {
  const useApiProducts = FeatureFlags.isEnabled('api_products');

  const products = useApiProducts
    ? useProductsFromApi()
    : useProductsFromMock();

  // ... rest of component
};
```

### **10.2 Database Migration Strategy**

#### **Data Migration Scripts**
```sql
-- Example: Migrate existing mock data to database
-- This ensures continuity during transition

-- Products migration
INSERT INTO products (name, description, price, brand_id, category_id, created_at)
SELECT
  mock_name,
  mock_description,
  mock_price,
  (SELECT id FROM brands WHERE name = mock_brand),
  (SELECT id FROM categories WHERE name = mock_category),
  NOW()
FROM mock_products_temp;

-- Categories migration
INSERT INTO categories (name, slug, description, created_at)
SELECT DISTINCT
  mock_category,
  LOWER(REPLACE(mock_category, ' ', '-')),
  CONCAT('دسته‌بندی ', mock_category),
  NOW()
FROM mock_products_temp;
```

### **10.3 Rollback Procedures**

#### **Automated Rollback Triggers**
```typescript
// Automated rollback based on error rates
class AutoRollback {
  private static errorThreshold = 0.05; // 5% error rate
  private static timeWindow = 5 * 60 * 1000; // 5 minutes

  static monitorAndRollback(): void {
    setInterval(() => {
      const metrics = ApiPerformanceMonitor.getMetrics();
      const recentErrors = this.calculateRecentErrorRate(metrics);

      if (recentErrors > this.errorThreshold) {
        console.error(`Error rate ${recentErrors * 100}% exceeds threshold, initiating rollback`);
        this.initiateRollback();
      }
    }, 60000); // Check every minute
  }

  private static calculateRecentErrorRate(metrics: Record<string, any>): number {
    const now = Date.now();
    let totalRequests = 0;
    let totalErrors = 0;

    Object.values(metrics).forEach((metric: any) => {
      if (now - metric.lastRequest < this.timeWindow) {
        totalRequests += metric.totalRequests;
        totalErrors += metric.errors;
      }
    });

    return totalRequests > 0 ? totalErrors / totalRequests : 0;
  }

  private static initiateRollback(): void {
    // Disable all API feature flags
    Object.keys(FeatureFlags['flags']).forEach(flag => {
      FeatureFlags['flags'][flag] = false;
    });

    // Clear API caches
    ApiCache.clear();

    // Notify monitoring systems
    ErrorReporter.reportError(new Error('Automated rollback initiated'), {
      action: 'auto_rollback',
      additionalData: { reason: 'high_error_rate' }
    });

    // Reload page to apply changes
    window.location.reload();
  }
}
```

---

## 11. Success Criteria and Validation

### **11.1 Technical Success Metrics**

#### **Performance Benchmarks**
- **API Response Time**: < 500ms for 95% of requests
- **Page Load Time**: < 3 seconds for product pages
- **Error Rate**: < 1% for all API endpoints
- **Cache Hit Rate**: > 80% for frequently accessed data
- **Database Query Time**: < 100ms for simple queries

#### **Functionality Validation**
- ✅ All product pages load from backend API
- ✅ Admin CRUD operations work correctly
- ✅ Search functionality uses backend data
- ✅ Reviews system fully functional
- ✅ Analytics dashboard shows real data
- ✅ Inventory management operational
- ✅ User management system working
- ✅ Notification system functional

### **11.2 Business Success Metrics**

#### **User Experience Metrics**
- **Customer Satisfaction**: Maintain current satisfaction levels
- **Task Completion Rate**: > 95% for critical user journeys
- **Error Recovery**: < 5 seconds to recover from API failures
- **Data Accuracy**: 100% accuracy for product information
- **Feature Parity**: All existing features remain functional

#### **Operational Metrics**
- **Admin Efficiency**: Reduce admin task completion time by 20%
- **Data Consistency**: 100% consistency between frontend and backend
- **System Reliability**: 99.9% uptime during business hours
- **Scalability**: Support 10x current user load
- **Maintainability**: Reduce code complexity by removing mock data

### **11.3 Validation Procedures**

#### **Automated Testing Suite**
```typescript
// Comprehensive test suite for API integration
describe('API Integration Tests', () => {
  describe('Product API', () => {
    test('should load products from backend', async () => {
      const products = await ProductApiService.getProducts();
      expect(products).toBeDefined();
      expect(products.length).toBeGreaterThan(0);
      expect(products[0]).toHaveProperty('id');
      expect(products[0]).toHaveProperty('name');
    });

    test('should handle API errors gracefully', async () => {
      // Mock API failure
      jest.spyOn(global, 'fetch').mockRejectedValueOnce(new Error('Network error'));

      const products = await ProductApiService.getProducts();
      expect(products).toBeDefined(); // Should return fallback data
    });
  });

  describe('Persian Language Support', () => {
    test('should display Persian text correctly', () => {
      const persianText = 'محصولات مراقبت از پوست';
      render(<ProductTitle title={persianText} />);
      expect(screen.getByText(persianText)).toBeInTheDocument();
    });

    test('should format Persian numbers correctly', () => {
      const price = 250000;
      const formatted = formatPrice(price);
      expect(formatted).toContain('۲۵۰,۰۰۰');
    });
  });
});
```

#### **Manual Testing Checklist**
- [ ] Admin login and authentication
- [ ] Product CRUD operations
- [ ] Category management
- [ ] Inventory updates
- [ ] Review submission and moderation
- [ ] Analytics data accuracy
- [ ] Notification system
- [ ] Search functionality
- [ ] Cart and wishlist persistence
- [ ] Mobile responsiveness
- [ ] Persian/RTL layout
- [ ] Error handling and recovery
- [ ] Performance under load
- [ ] Offline behavior

---

## 12. Conclusion and Next Steps

This comprehensive migration plan provides a systematic approach to replacing all mock data with real backend API integration while maintaining the high-quality user experience and Persian language support that characterizes the GlowRoya platform.

### **Immediate Next Steps**
1. **Review and Approve Plan**: Stakeholder review of migration strategy
2. **Environment Setup**: Ensure backend APIs are ready for integration
3. **Team Preparation**: Brief development team on implementation guidelines
4. **Monitoring Setup**: Implement performance and error monitoring
5. **Begin Phase 1**: Start with core admin functionality migration

### **Long-term Benefits**
- **Scalability**: Real backend integration supports business growth
- **Maintainability**: Cleaner codebase without mock data complexity
- **Reliability**: Consistent data across all user interactions
- **Performance**: Optimized API calls and caching strategies
- **User Experience**: Seamless, real-time data updates

### **Risk Mitigation**
- **Phased Approach**: Gradual rollout minimizes disruption
- **Feature Flags**: Instant rollback capability
- **Comprehensive Testing**: Thorough validation at each phase
- **Monitoring**: Real-time detection of issues
- **Fallback Mechanisms**: Graceful degradation when needed

The successful completion of this migration will transform GlowRoya from a demo application with mock data into a fully functional, production-ready e-commerce platform capable of serving real customers and supporting business operations at scale.
