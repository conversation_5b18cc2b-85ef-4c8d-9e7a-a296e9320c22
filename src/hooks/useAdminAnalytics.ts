import { useState, useEffect, useCallback } from 'react';
import { AnalyticsService } from '../services/apiService';
import {
  AnalyticsOverview,
  SalesAnalytics,
  CustomerAnalytics,
  TrafficAnalytics,
  AnalyticsFilters,
  ReportConfig,
  DailySalesData,
  ProductPerformance,
  CustomerSegment
} from '../types/adminAnalytics';







export const useAdminAnalytics = () => {
  const [overview, setOverview] = useState<AnalyticsOverview | null>(null);
  const [salesData, setSalesData] = useState<SalesAnalytics | null>(null);
  const [customerData, setCustomerData] = useState<CustomerAnalytics | null>(null);
  const [trafficData, setTrafficData] = useState<TrafficAnalytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<AnalyticsFilters>({
    dateRange: {
      start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      end: new Date().toISOString().split('T')[0]
    },
    period: 'day'
  });

  const fetchAnalyticsData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Convert date range to period format for API
      const apiFilters = {
        period: 'last30days', // Default period
        startDate: filters.dateRange.start,
        endDate: filters.dateRange.end
      };

      // Fetch all analytics data in parallel
      const [overviewResponse, salesResponse, customerResponse, trafficResponse] = await Promise.all([
        AnalyticsService.getOverview(apiFilters),
        AnalyticsService.getSalesAnalytics(apiFilters),
        AnalyticsService.getCustomerAnalytics(apiFilters),
        AnalyticsService.getTrafficAnalytics()
      ]);

      // Process overview data
      if (overviewResponse.success && overviewResponse.data) {
        const overview: AnalyticsOverview = {
          totalRevenue: overviewResponse.data.totalRevenue,
          totalOrders: overviewResponse.data.totalOrders,
          totalCustomers: overviewResponse.data.totalCustomers,
          averageOrderValue: overviewResponse.data.averageOrderValue,
          conversionRate: overviewResponse.data.conversionRate,
          revenueGrowth: overviewResponse.data.revenueGrowth,
          ordersGrowth: overviewResponse.data.ordersGrowth,
          customersGrowth: 0, // Calculate if needed
          period: overviewResponse.data.period,
          dateRange: overviewResponse.data.dateRange
        };
        setOverview(overview);
      }

      // Process sales data
      if (salesResponse.success && salesResponse.data) {
        const salesData: SalesAnalytics = {
          dailySales: salesResponse.data.dailySales.map((day: any) => ({
            date: day.date,
            revenue: Number(day.revenue) || 0,
            orders: Number(day.orders) || 0,
            averageOrderValue: Number(day.revenue) / Number(day.orders) || 0
          })),
          topProducts: salesResponse.data.topProducts.map((product: any) => ({
            id: product.id,
            name: product.name,
            revenue: product.revenue,
            unitsSold: product.quantity,
            category: product.brand || 'عمومی',
            image: product.image,
            growth: 0 // Calculate if needed
          })),
          revenueByChannel: salesResponse.data.paymentMethods.map((method: any) => ({
            channel: method.method === 'ONLINE' ? 'آنلاین' : method.method === 'CASH' ? 'نقدی' : 'سایر',
            revenue: method.revenue,
            percentage: (method.revenue / salesResponse.data.paymentMethods.reduce((sum: number, m: any) => sum + m.revenue, 0)) * 100,
            growth: 0
          })),
          period: salesResponse.data.period,
          dateRange: salesResponse.data.dateRange
        };
        setSalesData(salesData);
      }

      // Process customer data
      if (customerResponse.success && customerResponse.data) {
        const customerData: CustomerAnalytics = {
          newCustomers: customerResponse.data.newCustomers,
          returningCustomers: customerResponse.data.returningCustomers,
          customerLifetimeValue: customerResponse.data.averageLifetimeValue,
          customerSegments: [
            {
              segment: 'مشتریان جدید',
              count: customerResponse.data.newCustomers,
              percentage: (customerResponse.data.newCustomers / (customerResponse.data.newCustomers + customerResponse.data.returningCustomers)) * 100,
              averageOrderValue: customerResponse.data.averageLifetimeValue,
              totalRevenue: customerResponse.data.newCustomers * customerResponse.data.averageLifetimeValue,
              growth: 0
            },
            {
              segment: 'مشتریان بازگشتی',
              count: customerResponse.data.returningCustomers,
              percentage: (customerResponse.data.returningCustomers / (customerResponse.data.newCustomers + customerResponse.data.returningCustomers)) * 100,
              averageOrderValue: customerResponse.data.averageLifetimeValue,
              totalRevenue: customerResponse.data.returningCustomers * customerResponse.data.averageLifetimeValue,
              growth: 0
            }
          ],
          topCustomers: customerResponse.data.topCustomers.map((customer: any) => ({
            id: customer.id,
            name: customer.name,
            email: customer.email,
            totalOrders: customer.orderCount,
            totalRevenue: customer.totalRevenue,
            averageOrderValue: customer.averageOrderValue,
            lastOrderDate: customer.lastOrderDate,
            segment: customer.totalRevenue > 1000000 ? 'VIP' : customer.totalRevenue > 500000 ? 'طلایی' : 'نقره‌ای'
          })),
          period: customerResponse.data.period,
          dateRange: customerResponse.data.dateRange
        };
        setCustomerData(customerData);
      }

      // Process traffic data
      if (trafficResponse.success && trafficResponse.data) {
        const trafficData: TrafficAnalytics = {
          pageViews: trafficResponse.data.pageViews,
          uniqueVisitors: trafficResponse.data.uniqueVisitors,
          bounceRate: trafficResponse.data.bounceRate,
          averageSessionDuration: trafficResponse.data.averageSessionDuration,
          conversionRate: 0, // Calculate from other data if needed
          topPages: trafficResponse.data.topPages.map((page: any) => ({
            path: page.path,
            title: page.title,
            views: page.views,
            uniqueViews: page.views, // Assume same for now
            bounceRate: 0,
            averageTimeOnPage: 0
          })),
          trafficSources: trafficResponse.data.trafficSources.map((source: any) => ({
            source: source.source,
            visitors: source.sessions,
            percentage: source.percentage,
            conversionRate: 0,
            revenue: 0
          })),
          deviceTypes: trafficResponse.data.deviceTypes.map((device: any) => ({
            device: device.type,
            visitors: device.sessions,
            percentage: device.percentage,
            bounceRate: 0,
            conversionRate: 0
          })),
          period: 'last30days',
          dateRange: {
            start: filters.dateRange.start,
            end: filters.dateRange.end
          }
        };
        setTrafficData(trafficData);
      }

    } catch (err: any) {
      setError('خطا در بارگذاری داده‌های آماری');
      console.error('Analytics fetch error:', err);
    } finally {
      setLoading(false);
    }
  }, [filters]);

  const generateReport = useCallback(async (config: ReportConfig) => {
    try {
      // Simulate report generation
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // In a real app, this would trigger a download or email
      console.log('Report generated:', config);
      return { success: true, downloadUrl: '/reports/analytics-report.pdf' };
    } catch (err) {
      console.error('Report generation error:', err);
      return { success: false, error: 'خطا در تولید گزارش' };
    }
  }, []);

  const exportData = useCallback(async (type: string, format: string) => {
    try {
      // Simulate data export
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      console.log(`Exporting ${type} data as ${format}`);
      return { success: true, downloadUrl: `/exports/${type}-data.${format}` };
    } catch (err) {
      console.error('Export error:', err);
      return { success: false, error: 'خطا در خروجی گیری داده‌ها' };
    }
  }, []);

  useEffect(() => {
    fetchAnalyticsData();
  }, [fetchAnalyticsData]);

  return {
    overview,
    salesData,
    customerData,
    trafficData,
    loading,
    error,
    filters,
    setFilters,
    generateReport,
    exportData,
    refetch: fetchAnalyticsData
  };
};
