/**
 * Centralized API Service Layer for GlowRoya Admin Panel
 * Handles all HTTP requests to the backend API with authentication and error handling
 */

// API Configuration
const API_CONFIG = {
  BASE_URL: 'http://localhost:3001/api/v1',
  TIMEOUT: 30000,
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
} as const;

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  timestamp?: string;
}

export interface ApiError {
  success: false;
  error: {
    message: string;
    code: string;
    details?: any;
    stack?: string;
  };
  timestamp: string;
  path: string;
  method: string;
}

// Request Configuration
interface RequestConfig {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  body?: any;
  params?: Record<string, string | number>;
  requiresAuth?: boolean;
  timeout?: number;
}

// Persian Error Messages Mapping
const PERSIAN_ERROR_MESSAGES: Record<string, string> = {
  // Network errors
  'NETWORK_ERROR': 'خطا در اتصال به سرور',
  'TIMEOUT_ERROR': 'زمان اتصال به سرور به پایان رسید',
  'PARSE_ERROR': 'خطا در پردازش پاسخ سرور',
  'RATE_LIMIT_ERROR': 'تعداد درخواست‌ها بیش از حد مجاز است - لطفاً کمی صبر کنید',

  // Authentication errors
  'NO_TOKEN': 'توکن احراز هویت یافت نشد',
  'INVALID_TOKEN': 'توکن احراز هویت نامعتبر است',
  'EXPIRED_TOKEN': 'توکن احراز هویت منقضی شده است',
  'AUTH_ERROR': 'خطای احراز هویت',
  'INSUFFICIENT_PERMISSIONS': 'دسترسی مجاز نیست',

  // Validation errors
  'VALIDATION_ERROR': 'خطا در اعتبارسنجی داده‌ها',
  'REQUIRED_FIELD': 'این فیلد الزامی است',
  'INVALID_EMAIL': 'آدرس ایمیل نامعتبر است',
  'INVALID_PHONE': 'شماره تلفن نامعتبر است',

  // Resource errors
  'NOT_FOUND': 'منبع مورد نظر یافت نشد',
  'ALREADY_EXISTS': 'این منبع قبلاً وجود دارد',
  'CONFLICT': 'تداخل در داده‌ها',

  // Server errors
  'INTERNAL_SERVER_ERROR': 'خطای داخلی سرور',
  'SERVICE_UNAVAILABLE': 'سرویس در دسترس نیست',
  'DATABASE_ERROR': 'خطا در پایگاه داده',

  // Default
  'UNKNOWN_ERROR': 'خطای نامشخص رخ داده است'
};

/**
 * Token Management Utilities
 */
class TokenManager {
  private static readonly TOKEN_KEY = 'admin_auth_token';
  private static readonly REFRESH_TOKEN_KEY = 'admin_refresh_token';

  static getToken(): string | null {
    return sessionStorage.getItem(this.TOKEN_KEY) || 
           localStorage.getItem(this.TOKEN_KEY);
  }

  static setToken(token: string, rememberMe: boolean = false): void {
    const storage = rememberMe ? localStorage : sessionStorage;
    storage.setItem(this.TOKEN_KEY, token);
  }

  static getRefreshToken(): string | null {
    return sessionStorage.getItem(this.REFRESH_TOKEN_KEY) || 
           localStorage.getItem(this.REFRESH_TOKEN_KEY);
  }

  static setRefreshToken(token: string, rememberMe: boolean = false): void {
    const storage = rememberMe ? localStorage : sessionStorage;
    storage.setItem(this.REFRESH_TOKEN_KEY, token);
  }

  static clearTokens(): void {
    sessionStorage.removeItem(this.TOKEN_KEY);
    sessionStorage.removeItem(this.REFRESH_TOKEN_KEY);
    localStorage.removeItem(this.TOKEN_KEY);
    localStorage.removeItem(this.REFRESH_TOKEN_KEY);
  }

  static isTokenExpired(token: string): boolean {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return payload.exp * 1000 < Date.now();
    } catch {
      return true;
    }
  }
}

/**
 * HTTP Client with automatic retry and error handling
 */
class HttpClient {
  // Request throttling to prevent rate limiting
  private static requestQueue: Map<string, number> = new Map();
  private static readonly REQUEST_DELAY = 100; // 100ms between requests to same endpoint

  private static async throttleRequest(endpoint: string): Promise<void> {
    const lastRequest = this.requestQueue.get(endpoint) || 0;
    const now = Date.now();
    const timeSinceLastRequest = now - lastRequest;

    if (timeSinceLastRequest < this.REQUEST_DELAY) {
      const delay = this.REQUEST_DELAY - timeSinceLastRequest;
      await new Promise(resolve => setTimeout(resolve, delay));
    }

    this.requestQueue.set(endpoint, Date.now());
  }

  private static async makeRequest<T>(
    endpoint: string,
    config: RequestConfig
  ): Promise<ApiResponse<T>> {
    // Throttle requests to prevent rate limiting
    await this.throttleRequest(endpoint);

    // Construct URL properly - concatenate base URL with endpoint
    const baseUrl = API_CONFIG.BASE_URL.endsWith('/') ? API_CONFIG.BASE_URL.slice(0, -1) : API_CONFIG.BASE_URL;
    const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;
    const url = new URL(`${baseUrl}/${cleanEndpoint}`);
    
    // Add query parameters
    if (config.params) {
      Object.entries(config.params).forEach(([key, value]) => {
        url.searchParams.append(key, String(value));
      });
    }

    // Prepare headers
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      ...config.headers,
    };

    // Add authentication header if required
    if (config.requiresAuth !== false) {
      const token = TokenManager.getToken();
      if (token) {
        headers.Authorization = `Bearer ${token}`;
      }
    }

    // Prepare request options
    const requestOptions: RequestInit = {
      method: config.method,
      headers,
      signal: AbortSignal.timeout(config.timeout || API_CONFIG.TIMEOUT),
    };

    // Add body for non-GET requests
    if (config.body && config.method !== 'GET') {
      if (config.body instanceof FormData) {
        delete headers['Content-Type']; // Let browser set it for FormData
        requestOptions.body = config.body;
      } else {
        requestOptions.body = JSON.stringify(config.body);
      }
    }

    try {
      const response = await fetch(url.toString(), requestOptions);
      
      // Handle non-JSON responses
      const contentType = response.headers.get('content-type');
      if (!contentType?.includes('application/json')) {
        throw new Error('PARSE_ERROR');
      }

      const data = await response.json();

      // Handle HTTP error status codes
      if (!response.ok) {
        // Handle rate limiting specifically
        if (response.status === 429) {
          throw new Error('RATE_LIMIT_ERROR');
        }

        const error: ApiError = data;
        throw new Error(error.error?.code || 'UNKNOWN_ERROR');
      }

      return data as ApiResponse<T>;
    } catch (error) {
      if (error instanceof Error) {
        // Handle specific error types
        if (error.name === 'AbortError') {
          throw new Error('TIMEOUT_ERROR');
        }
        if (error.message.includes('Failed to fetch')) {
          throw new Error('NETWORK_ERROR');
        }
        throw error;
      }
      throw new Error('UNKNOWN_ERROR');
    }
  }

  private static async retryRequest<T>(
    endpoint: string, 
    config: RequestConfig,
    attempt: number = 1
  ): Promise<ApiResponse<T>> {
    try {
      return await this.makeRequest<T>(endpoint, config);
    } catch (error) {
      const errorCode = error instanceof Error ? error.message : 'UNKNOWN_ERROR';
      
      // Don't retry authentication errors or client errors
      const nonRetryableErrors = [
        'INVALID_TOKEN', 'EXPIRED_TOKEN', 'NO_TOKEN',
        'INSUFFICIENT_PERMISSIONS', 'VALIDATION_ERROR'
      ];

      if (nonRetryableErrors.includes(errorCode) || attempt >= API_CONFIG.RETRY_ATTEMPTS) {
        throw new Error(errorCode);
      }

      // For rate limiting, wait longer before retry
      const delay = errorCode === 'RATE_LIMIT_ERROR'
        ? API_CONFIG.RETRY_DELAY * attempt * 3 // 3x longer delay for rate limits
        : API_CONFIG.RETRY_DELAY * attempt;

      await new Promise(resolve => setTimeout(resolve, delay));

      return this.retryRequest<T>(endpoint, config, attempt + 1);
    }
  }

  // Public HTTP methods
  static async get<T>(
    endpoint: string, 
    params?: Record<string, string | number>,
    requiresAuth: boolean = true
  ): Promise<ApiResponse<T>> {
    return this.retryRequest<T>(endpoint, {
      method: 'GET',
      params,
      requiresAuth,
    });
  }

  static async post<T>(
    endpoint: string, 
    body?: any,
    requiresAuth: boolean = true
  ): Promise<ApiResponse<T>> {
    return this.retryRequest<T>(endpoint, {
      method: 'POST',
      body,
      requiresAuth,
    });
  }

  static async put<T>(
    endpoint: string, 
    body?: any,
    requiresAuth: boolean = true
  ): Promise<ApiResponse<T>> {
    return this.retryRequest<T>(endpoint, {
      method: 'PUT',
      body,
      requiresAuth,
    });
  }

  static async delete<T>(
    endpoint: string,
    requiresAuth: boolean = true
  ): Promise<ApiResponse<T>> {
    return this.retryRequest<T>(endpoint, {
      method: 'DELETE',
      requiresAuth,
    });
  }

  static async patch<T>(
    endpoint: string,
    body?: any,
    requiresAuth: boolean = true
  ): Promise<ApiResponse<T>> {
    return this.retryRequest<T>(endpoint, {
      method: 'PATCH',
      body,
      requiresAuth,
    });
  }
}

// Inventory Management Service
export class InventoryService {
  static async getInventory(filters?: {
    status?: string;
    category?: string;
    brand?: string;
    search?: string;
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: string;
  }): Promise<ApiResponse<{
    inventory: any[];
    pagination: any;
    summary: any;
  }>> {
    const params = new URLSearchParams();
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          params.append(key, value.toString());
        }
      });
    }

    return ApiService.get(`/inventory?${params.toString()}`);
  }

  static async getInventoryStats(): Promise<ApiResponse<{
    totalProducts: number;
    inStock: number;
    lowStock: number;
    outOfStock: number;
    totalQuantity: number;
    stockHealth: {
      healthy: number;
      warning: number;
      critical: number;
    };
  }>> {
    return ApiService.get('/inventory/stats');
  }

  static async getLowStockItems(): Promise<ApiResponse<{
    items: any[];
    count: number;
  }>> {
    return ApiService.get('/inventory/low-stock');
  }

  static async updateInventory(id: string, data: {
    quantity: number;
    lowStockThreshold?: number;
    allowBackorder?: boolean;
    notes?: string;
  }): Promise<ApiResponse<any>> {
    return ApiService.put(`/inventory/${id}`, data);
  }
}

// Analytics Service
export class AnalyticsService {
  static async getOverview(filters?: {
    period?: string;
    startDate?: string;
    endDate?: string;
    category?: string;
    brand?: string;
  }): Promise<ApiResponse<{
    totalRevenue: number;
    totalOrders: number;
    totalCustomers: number;
    averageOrderValue: number;
    conversionRate: number;
    revenueGrowth: number;
    ordersGrowth: number;
    period: string;
    dateRange: {
      start: string;
      end: string;
    };
  }>> {
    const params = new URLSearchParams();
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          params.append(key, value.toString());
        }
      });
    }

    return ApiService.get(`/analytics/overview?${params.toString()}`);
  }

  static async getSalesAnalytics(filters?: {
    period?: string;
    startDate?: string;
    endDate?: string;
  }): Promise<ApiResponse<{
    dailySales: any[];
    topProducts: any[];
    paymentMethods: any[];
    period: string;
    dateRange: {
      start: string;
      end: string;
    };
  }>> {
    const params = new URLSearchParams();
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          params.append(key, value.toString());
        }
      });
    }

    return ApiService.get(`/analytics/sales?${params.toString()}`);
  }

  static async getCustomerAnalytics(filters?: {
    period?: string;
    startDate?: string;
    endDate?: string;
  }): Promise<ApiResponse<{
    newCustomers: number;
    returningCustomers: number;
    averageLifetimeValue: number;
    topCustomers: any[];
    period: string;
    dateRange: {
      start: string;
      end: string;
    };
  }>> {
    const params = new URLSearchParams();
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          params.append(key, value.toString());
        }
      });
    }

    return ApiService.get(`/analytics/customers?${params.toString()}`);
  }

  static async getTrafficAnalytics(): Promise<ApiResponse<{
    pageViews: number;
    uniqueVisitors: number;
    bounceRate: number;
    averageSessionDuration: number;
    topPages: any[];
    deviceTypes: any[];
    trafficSources: any[];
  }>> {
    return ApiService.get('/analytics/traffic');
  }
}

/**
 * API Error Handler
 */
export class ApiErrorHandler {
  static handleError(error: Error): string {
    const errorCode = error.message;
    return PERSIAN_ERROR_MESSAGES[errorCode] || PERSIAN_ERROR_MESSAGES.UNKNOWN_ERROR;
  }

  static isAuthError(error: Error): boolean {
    const authErrors = ['NO_TOKEN', 'INVALID_TOKEN', 'EXPIRED_TOKEN', 'AUTH_ERROR'];
    return authErrors.includes(error.message);
  }

  static shouldRetry(error: Error): boolean {
    const retryableErrors = ['NETWORK_ERROR', 'TIMEOUT_ERROR', 'SERVICE_UNAVAILABLE'];
    return retryableErrors.includes(error.message);
  }
}

/**
 * Main API Service Class
 */
export class ApiService {
  // Token management
  static TokenManager = TokenManager;

  // HTTP client
  static Http = HttpClient;

  // Error handling
  static ErrorHandler = ApiErrorHandler;

  // Health check cache
  private static healthCheckCache: {
    result: boolean;
    timestamp: number;
    ttl: number;
  } = {
    result: false,
    timestamp: 0,
    ttl: 30000 // 30 seconds cache
  };

  // Health check (uses different base URL since health is not under /api/v1)
  static async healthCheck(): Promise<boolean> {
    const now = Date.now();

    // Return cached result if still valid
    if (now - this.healthCheckCache.timestamp < this.healthCheckCache.ttl) {
      return this.healthCheckCache.result;
    }

    try {
      const healthUrl = 'http://localhost:3001/health';
      const response = await fetch(healthUrl, {
        method: 'GET',
        cache: 'no-cache',
        signal: AbortSignal.timeout(5000) // 5 second timeout
      });

      const result = response.ok;

      // Update cache
      this.healthCheckCache = {
        result,
        timestamp: now,
        ttl: result ? 30000 : 10000 // Cache success for 30s, failure for 10s
      };

      return result;
    } catch {
      // Update cache with failure
      this.healthCheckCache = {
        result: false,
        timestamp: now,
        ttl: 10000 // Cache failure for 10s
      };

      return false;
    }
  }

  // Test API connection
  static async testConnection(): Promise<{ success: boolean; message: string }> {
    try {
      const response = await HttpClient.get('/docs', undefined, false);
      return {
        success: true,
        message: 'اتصال به API با موفقیت برقرار شد'
      };
    } catch (error) {
      return {
        success: false,
        message: ApiErrorHandler.handleError(error as Error)
      };
    }
  }

  // Admin login
  static async adminLogin(email: string, password: string): Promise<{
    success: boolean;
    data?: {
      user: any;
      token: string;
      refreshToken: string;
    };
    message: string;
  }> {
    try {
      const response = await HttpClient.post<{
        user: any;
        token: string;
        refreshToken: string;
        expiresIn: string;
      }>('/auth/login', {
        email,
        password
      }, false);

      if (response.success && response.data) {
        // Store tokens
        TokenManager.setToken(response.data.token, true);
        TokenManager.setRefreshToken(response.data.refreshToken, true);

        return {
          success: true,
          data: response.data,
          message: 'ورود با موفقیت انجام شد'
        };
      }

      throw new Error('LOGIN_FAILED');
    } catch (error) {
      return {
        success: false,
        message: ApiErrorHandler.handleError(error as Error)
      };
    }
  }

  // Check if user is authenticated
  static isAuthenticated(): boolean {
    const token = TokenManager.getToken();
    if (!token) return false;

    return !TokenManager.isTokenExpired(token);
  }

  // Get current user info from token
  static getCurrentUserFromToken(): any {
    const token = TokenManager.getToken();
    if (!token) return null;

    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return {
        id: payload.userId,
        email: payload.email,
        role: payload.role
      };
    } catch {
      return null;
    }
  }
}

export default ApiService;
